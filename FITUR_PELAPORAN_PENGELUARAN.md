# Fitur Pelaporan Pengeluaran dan Omset Bersih

## Overview
Fitur baru yang ditambahkan untuk memantau omset bersih kangider dengan mengurangi pengeluaran operasional dan internal usage dari total pendapatan.

## Fitur yang Ditambahkan

### 1. **Card Omset Bersih**
- Menampilkan omset bersih (Total Pendapatan - Total Pengeluaran)
- Warna hijau untuk omset positif, merah untuk omset negatif
- Icon berubah sesuai dengan status omset

### 2. **Card Total Pengeluaran**
- Menampilkan total pengeluaran (Operational Expenses + Internal Usage)
- Menampilkan jumlah total transaksi pengeluaran

### 3. **Detail Pengeluaran**
- **Pengeluaran Operasional**: Parkir, dll dengan keterangan
- **Penggunaan Internal**: Produk yang digunakan internal dengan quantity dan harga
- Subtotal untuk masing-masing kategori
- Total keseluruhan pengeluaran

## Schema Data

### Operational Expenses
```json
{
  "id": 4,
  "status": "draft",
  "user_created": "1cc75752-9a12-489f-a267-1c9537b0ccf9",
  "date_created": "2025-07-22T07:46:41.223Z",
  "expense_name": "parkir",
  "category": null,
  "amount": 4000,
  "keterangan": "bayar parkir",
  "expense_date": "2025-07-22",
  "waktu": "14:46:00",
  "kangider": "c64315fa-5565-4543-aa5e-c23d30c2a1c8"
}
```

### Internal Usage
```json
{
  "id": 11,
  "status": "draft",
  "user_created": "f41528a2-b4de-4cf5-8f39-73b9e848ceb7",
  "date_created": "2025-07-22T12:29:28.174Z",
  "quantity": "6",
  "unit_price": "10000",
  "total_amount": 60000,
  "usage_reason": "",
  "item_id": 4,
  "waktu": "19:29:00",
  "usage_date": "2025-07-22",
  "kangider": "8d096913-7ad6-4069-94e4-bf8e5f84348a"
}
```

## API Endpoints

### Operational Expenses
```
GET https://api.iderkopi.id/items/operational_expenses
```

### Internal Usage
```
GET https://api.iderkopi.id/items/internal_usage
```

## Perhitungan

### Omset Bersih
```
Omset Bersih = Total Pendapatan - (Total Operational Expenses + Total Internal Usage)
```

### Total Pengeluaran
```
Total Pengeluaran = Σ(operational_expenses.amount) + Σ(internal_usage.total_amount)
```

## Filter dan Integrasi

- **Filter Tanggal**: Menggunakan `expense_date` dan `usage_date`
- **Filter Kangider**: Menggunakan field `kangider` untuk filtering per kangider
- **Responsive Design**: Grid yang responsif untuk berbagai ukuran layar

## Tampilan Dashboard

### Cards (5 kolom responsif):
1. Total Pembayaran Cash
2. Total Pembayaran QRIS  
3. Total Pendapatan
4. **Total Pengeluaran** (Baru)
5. **Omset Bersih** (Baru)

### Detail Section (3 kolom responsif):
1. Daftar Menu dan Penjualan
2. **Detail Pengeluaran** (Baru)
3. Lokasi Berjualan

## Error Handling

- Jika API pengeluaran tidak tersedia, akan menampilkan warning di console
- Data pengeluaran akan di-set sebagai array kosong jika API gagal
- Perhitungan tetap berjalan dengan nilai 0 untuk pengeluaran

## Fitur Grafik dan Visualisasi (Update Terbaru)

### 1. **Perbaikan Tampilan Daftar Menu dan Penjualan**
- ✅ Tampilan tabel yang lebih menarik dengan color coding
- ✅ Progress bar untuk visualisasi stock terjual
- ✅ Status stock (Habis/Sedikit/Cukup) dengan badge berwarna
- ✅ Summary section dengan total cup terjual
- ✅ Responsive table dengan scroll horizontal

### 2. **Grafik Produk Terlaris**
- Bar chart menampilkan 8 produk terlaris
- Diurutkan berdasarkan jumlah cup terjual
- Tooltip menampilkan detail penjualan
- Responsive design dengan rotated labels

### 3. **Grafik Capaian Omset per Kangider**
- Multi-bar chart menampilkan pendapatan, pengeluaran, dan omset bersih
- Filter otomatis kangider yang memiliki data
- Color coding: hijau (pendapatan), merah (pengeluaran), biru (omset)

### 4. **Grafik Rata-rata Stock per Kangider**
- Perbandingan rata-rata stock awal vs stock akhir
- Membantu analisis efisiensi stock management
- Data dihitung berdasarkan semua item per kangider

### 5. **Grafik Trend Penjualan Harian**
- Line chart menampilkan trend penjualan per hari
- Tooltip menampilkan total penjualan dan jumlah transaksi
- Membantu identifikasi pola penjualan harian

## Layout Dashboard Terbaru (Update Final)

### Section 1: Cards Metrics (5 kolom responsif)
1. Total Pembayaran Cash
2. Total Pembayaran QRIS
3. Total Pendapatan
4. Total Pengeluaran
5. Omset Bersih

### Section 2: Analisis dan Grafik (2 kolom responsif)
1. Grafik Produk Terlaris
2. Grafik Capaian Omset per Kangider

### Section 3: Analisis Lanjutan (2 kolom responsif)
1. **Grafik Stock Harian per Kangider** (Updated)
2. Grafik Trend Penjualan Harian

### Section 4: Detail Data (2 kolom responsif - Layout Diperbaiki)
1. **Daftar Menu dan Penjualan** (Enhanced - 2/3 lebar layar)
2. **Detail Pengeluaran + Lokasi Berjualan** (1/3 lebar layar, stacked vertical)

## Perbaikan Layout dan Grafik (Update Terbaru)

### 1. **Perbaikan Layout Detail Data**
- ✅ **Layout 2 kolom** untuk memberikan ruang lebih luas pada tabel menu
- ✅ **Daftar Menu dan Penjualan** mendapat 2/3 lebar layar (col-span-2)
- ✅ **Detail Pengeluaran dan Lokasi** digabung dalam 1 kolom dengan layout vertikal
- ✅ **Responsive design** yang lebih baik untuk berbagai ukuran layar

### 2. **Perbaikan Grafik Stock Harian**
- ✅ **Grafik Stock Harian per Kangider** (bukan rata-rata)
- ✅ **Perbandingan stock yang dibawa setiap kangider per hari**
- ✅ **Multi-bar chart** dengan warna berbeda untuk setiap kangider
- ✅ **Menampilkan 7 hari terakhir** untuk analisis trend
- ✅ **Color coding otomatis** berdasarkan jumlah kangider

## Fitur Export Excel (Update Terbaru)

### 📊 **Fitur Export Laporan ke Excel**
- ✅ **Tombol Export Excel** di header dashboard dengan styling hijau
- ✅ **Multi-sheet Excel file** dengan data terorganisir
- ✅ **Nama file otomatis** berdasarkan periode laporan
- ✅ **Data lengkap** semua aspek dashboard

### 📋 **Struktur File Excel**

#### **Sheet 1: Ringkasan**
- Header laporan dengan periode dan filter
- Ringkasan keuangan lengkap:
  - Total Pembayaran Cash & QRIS
  - Total Pendapatan
  - Total Pengeluaran (Operasional + Internal)
  - Omset Bersih

#### **Sheet 2: Penjualan**
- Data detail semua transaksi penjualan
- Kolom: ID, Tanggal, Total Harga, Metode Bayar, Status, Kang Ider

#### **Sheet 3: Menu & Stock**
- Data lengkap menu dan stock
- Kolom: Menu, Harga, Stock Awal, Terjual, Stock Akhir, Nilai Penjualan, Status Stock

#### **Sheet 4: Pengeluaran Operasional** (jika ada data)
- Detail semua pengeluaran operasional
- Kolom: ID, Tanggal, Waktu, Nama Pengeluaran, Kategori, Jumlah, Keterangan, Kang Ider

#### **Sheet 5: Penggunaan Internal** (jika ada data)
- Detail semua penggunaan internal
- Kolom: ID, Tanggal, Waktu, Item ID, Quantity, Harga Satuan, Total Amount, Alasan, Kang Ider

### 🎯 **Keunggulan Fitur Export**
1. **Data Terstruktur** - Setiap jenis data dalam sheet terpisah
2. **Format Siap Pakai** - Header dan formatting yang rapi
3. **Nama File Otomatis** - Format: `Laporan_Dashboard_YYYY-MM-DD_YYYY-MM-DD.xlsx`
4. **Filter Terintegrasi** - Export sesuai dengan filter yang dipilih
5. **Error Handling** - Notifikasi jika terjadi kesalahan

## Perbaikan Stock Akumulasi (Update Critical)

### 🐛 **Bug Fix: Logika Stock Akumulasi**
- ✅ **Masalah**: Stock awal dan akhir dijumlahkan dari semua hari (salah)
- ✅ **Solusi**: Stock awal dari hari pertama, stock akhir dari hari terakhir
- ✅ **Dampak**: Data stock akurat untuk filter rentang tanggal
- ✅ **Affected**: Dashboard UI, Export Excel, Grafik Stock

### 🔧 **Technical Implementation**
- **Date Tracking**: earliest_date dan latest_date per produk
- **Stock Records**: Array tracking semua record stock per hari
- **Logic Fix**: Perbandingan tanggal untuk menentukan stock awal/akhir yang benar

### ✅ **Validation**
- **Single Day**: Stock awal = stock akhir (benar)
- **Multiple Days**: Stock awal dari hari pertama, stock akhir dari hari terakhir
- **Export Excel**: Data konsisten dengan dashboard
- **UI Components**: Progress bar dan status stock akurat

## Testing

Untuk testing fitur ini:
1. Login ke dashboard
2. Pilih rentang tanggal yang memiliki data pengeluaran
3. Pilih kangider tertentu atau "Semua"
4. **Baru**: Klik tombol "Export Excel" di header
5. **Baru**: Verifikasi file Excel ter-download dengan nama yang sesuai
6. **Baru**: Buka file Excel dan cek semua sheet data
7. Verifikasi perhitungan omset bersih
8. Cek detail pengeluaran di card "Detail Pengeluaran"
9. Scroll ke bawah untuk melihat grafik-grafik analisis
10. Cek tampilan tabel menu yang sudah diperbaiki dengan total cup terjual
